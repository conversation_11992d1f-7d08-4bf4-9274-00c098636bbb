<script lang="ts" setup>
import {
  ArrowLeft,
  ArrowRight,
  FullScreen,
  Loading,
  Microphone,
  Mute,
  RefreshLeft,
  RefreshRight,
  VideoPause,
  VideoPlay,
  ZoomIn,
  ZoomOut
} from "@element-plus/icons-vue"
import { ElMessage } from "element-plus"
import { computed, onMounted, onUnmounted, ref } from "vue"

// 响应式数据
const videoContainer = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
const currentVideo = ref("")
const videoList = ref<string[]>([])
const currentIndex = ref(0)
const scale = ref(1)
const rotation = ref(0)
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const videoPosition = ref({ x: 0, y: 0 })

// 视频播放控制
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(false)
const playbackRate = ref(1)
const isLoading = ref(false)

// 模拟视频数据
const mockVideos = [
  "/src/common/assets/video/3099938-hd_1920_1080_30fps.mp4",
  "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
  "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
]

// 计算属性
const progressPercentage = computed(() => {
  return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
})

const formattedCurrentTime = computed(() => formatTime(currentTime.value))
const formattedDuration = computed(() => formatTime(duration.value))

// 格式化时间
function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
}

// 初始化
onMounted(() => {
  videoList.value = mockVideos
  if (videoList.value.length > 0) {
    currentVideo.value = videoList.value[0]
  }

  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown)
})

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  switch (event.key) {
    case "ArrowLeft":
      previousVideo()
      break
    case "ArrowRight":
      nextVideo()
      break
    case " ":
      event.preventDefault()
      togglePlay()
      break
    case "+":
    case "=":
      zoomIn()
      break
    case "-":
      zoomOut()
      break
    case "r":
    case "R":
      rotateRight()
      break
    case "Escape":
      resetView()
      break
    case "m":
    case "M":
      toggleMute()
      break
  }
}

// 视频操作方法
function previousVideo() {
  if (currentIndex.value > 0) {
    currentIndex.value--
    currentVideo.value = videoList.value[currentIndex.value]
    resetView()
  }
}

function nextVideo() {
  if (currentIndex.value < videoList.value.length - 1) {
    currentIndex.value++
    currentVideo.value = videoList.value[currentIndex.value]
    resetView()
  }
}

// 视频播放控制
function togglePlay() {
  if (videoElement.value) {
    if (isPlaying.value) {
      videoElement.value.pause()
    } else {
      videoElement.value.play()
    }
  }
}

function toggleMute() {
  if (videoElement.value) {
    isMuted.value = !isMuted.value
    videoElement.value.muted = isMuted.value
  }
}

function setVolume(value: number | number[]) {
  const vol = Array.isArray(value) ? value[0] : value
  volume.value = vol
  if (videoElement.value) {
    videoElement.value.volume = vol
  }
}

function setPlaybackRate(rate: number) {
  playbackRate.value = rate
  if (videoElement.value) {
    videoElement.value.playbackRate = rate
  }
}

function seekTo(event: MouseEvent) {
  const progressContainer = event.currentTarget as HTMLElement
  const rect = progressContainer.getBoundingClientRect()
  const percentage = ((event.clientX - rect.left) / rect.width) * 100

  if (videoElement.value && duration.value > 0) {
    const time = (percentage / 100) * duration.value
    videoElement.value.currentTime = time
  }
}

// 视图操作方法
function zoomIn() {
  scale.value = Math.min(scale.value * 1.2, 5)
}

function zoomOut() {
  scale.value = Math.max(scale.value / 1.2, 0.1)
}

function rotateLeft() {
  rotation.value -= 90
}

function rotateRight() {
  rotation.value += 90
}

function resetView() {
  scale.value = 1
  rotation.value = 0
  videoPosition.value = { x: 0, y: 0 }
}

function fullScreen() {
  if (videoContainer.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      videoContainer.value.requestFullscreen()
    }
  }
}

// 视频事件处理
function handleVideoLoadedMetadata() {
  if (videoElement.value) {
    duration.value = videoElement.value.duration
  }
}

function handleVideoTimeUpdate() {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime
  }
}

function handleVideoPlay() {
  isPlaying.value = true
}

function handleVideoPause() {
  isPlaying.value = false
}

function handleVideoEnded() {
  isPlaying.value = false
  currentTime.value = 0
}

function handleVideoLoadStart() {
  isLoading.value = true
}

function handleVideoCanPlay() {
  isLoading.value = false
}

function handleVideoError() {
  ElMessage.error("视频加载失败")
  isLoading.value = false
}

// 鼠标拖拽
function handleMouseDown(event: MouseEvent) {
  isDragging.value = true
  dragStart.value = {
    x: event.clientX - videoPosition.value.x,
    y: event.clientY - videoPosition.value.y
  }
}

function handleMouseMove(event: MouseEvent) {
  if (isDragging.value) {
    videoPosition.value = {
      x: event.clientX - dragStart.value.x,
      y: event.clientY - dragStart.value.y
    }
  }
}

function handleMouseUp() {
  isDragging.value = false
}

// 鼠标滚轮缩放
function handleWheel(event: WheelEvent) {
  event.preventDefault()
  if (event.deltaY < 0) {
    zoomIn()
  } else {
    zoomOut()
  }
}

// 计算视频样式
const videoStyle = computed(() => ({
  transform: `translate(${videoPosition.value.x}px, ${videoPosition.value.y}px) scale(${scale.value}) rotate(${rotation.value}deg)`,
  cursor: isDragging.value ? "grabbing" : "grab"
}))
</script>

<template>
  <div class="video-viewer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 播放控制 -->
        <el-button-group>
          <el-button
            :icon="isPlaying ? VideoPause : VideoPlay"
            @click="togglePlay"
            :title="isPlaying ? '暂停 (空格)' : '播放 (空格)'"
          >
            {{ isPlaying ? '暂停' : '播放' }}
          </el-button>
        </el-button-group>

        <!-- 视图控制 -->
        <el-button-group class="ml-2">
          <el-button :icon="ZoomOut" @click="zoomOut" title="缩小 (-)">
            缩小
          </el-button>
          <el-button @click="resetView" title="重置 (Esc)">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button :icon="ZoomIn" @click="zoomIn" title="放大 (+)">
            放大
          </el-button>
        </el-button-group>

        <el-button-group class="ml-2">
          <el-button :icon="RefreshLeft" @click="rotateLeft" title="逆时针旋转">
            逆时针
          </el-button>
          <el-button :icon="RefreshRight" @click="rotateRight" title="顺时针旋转 (R)">
            顺时针
          </el-button>
        </el-button-group>

        <!-- 音频控制 -->
        <el-button-group class="ml-2">
          <el-button
            :icon="isMuted ? Mute : Microphone"
            @click="toggleMute"
            title="静音切换 (M)"
          >
            {{ isMuted ? '取消静音' : '静音' }}
          </el-button>
        </el-button-group>

        <el-button :icon="FullScreen" @click="fullScreen" class="ml-2" title="全屏">
          全屏
        </el-button>
      </div>

      <div class="toolbar-right">
        <span class="video-info">
          {{ currentIndex + 1 }} / {{ videoList.length }}
        </span>
      </div>
    </div>

    <!-- 视频播放控制条 -->
    <div class="video-controls">
      <div class="time-info">
        <span>{{ formattedCurrentTime }}</span>
      </div>

      <div class="progress-container" @click="seekTo">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${progressPercentage}%` }"
          />
        </div>
      </div>

      <div class="time-info">
        <span>{{ formattedDuration }}</span>
      </div>

      <div class="volume-control">
        <el-slider
          v-model="volume"
          :min="0"
          :max="1"
          :step="0.1"
          :show-tooltip="false"
          @change="setVolume"
          style="width: 80px;"
        />
      </div>

      <div class="playback-rate">
        <el-select v-model="playbackRate" @change="setPlaybackRate" style="width: 80px;">
          <el-option label="0.5x" :value="0.5" />
          <el-option label="1.0x" :value="1" />
          <el-option label="1.25x" :value="1.25" />
          <el-option label="1.5x" :value="1.5" />
          <el-option label="2.0x" :value="2" />
        </el-select>
      </div>
    </div>

    <!-- 视频显示区域 -->
    <div
      ref="videoContainer"
      class="video-container"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
      @wheel="handleWheel"
    >
      <video
        v-if="currentVideo"
        ref="videoElement"
        :src="currentVideo"
        :style="videoStyle"
        class="medical-video"
        @loadedmetadata="handleVideoLoadedMetadata"
        @timeupdate="handleVideoTimeUpdate"
        @play="handleVideoPlay"
        @pause="handleVideoPause"
        @ended="handleVideoEnded"
        @loadstart="handleVideoLoadStart"
        @canplay="handleVideoCanPlay"
        @error="handleVideoError"
        preload="metadata"
      />
      <div v-else class="no-video">
        <p>暂无视频数据</p>
      </div>

      <!-- 加载指示器 -->
      <div v-if="isLoading" class="loading-indicator">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <p>视频加载中...</p>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation">
      <el-button
        class="nav-btn nav-prev"
        :disabled="currentIndex <= 0"
        @click="previousVideo"
        title="上一个视频 (←)"
      >
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <el-button
        class="nav-btn nav-next"
        :disabled="currentIndex >= videoList.length - 1"
        @click="nextVideo"
        title="下一个视频 (→)"
      >
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>

    <!-- 快捷键提示 -->
    <div class="shortcuts-hint">
      <p>快捷键：← → 切换视频 | 空格 播放/暂停 | + - 缩放 | R 旋转 | M 静音 | Esc 重置</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-viewer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #fff;
  position: relative;
  overflow: hidden;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
    z-index: 10;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .ml-2 {
        margin-left: 8px;
      }
    }

    .toolbar-right {
      .video-info {
        font-size: 14px;
        color: #ccc;
      }
    }
  }

  .video-controls {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
    gap: 12px;

    .time-info {
      font-size: 12px;
      color: #ccc;
      min-width: 40px;
    }

    .progress-container {
      flex: 1;
      cursor: pointer;

      .progress-bar {
        height: 4px;
        background-color: #404040;
        border-radius: 2px;
        position: relative;

        .progress-fill {
          height: 100%;
          background-color: #409eff;
          border-radius: 2px;
          transition: width 0.1s ease;
        }
      }
    }

    .volume-control {
      display: flex;
      align-items: center;
    }

    .playback-rate {
      min-width: 80px;
    }
  }

  .video-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background:
      radial-gradient(circle at 25% 25%, #333 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, #333 1px, transparent 1px);
    background-size: 20px 20px;
    background-position:
      0 0,
      10px 10px;

    .medical-video {
      max-width: none;
      max-height: none;
      transition: transform 0.1s ease-out;
      user-select: none;
      -webkit-user-drag: none;
    }

    .no-video {
      text-align: center;
      color: #666;
      font-size: 18px;
    }

    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #ccc;

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }
  }

  .navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 5;

    .nav-btn {
      position: absolute;
      top: 0;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      font-size: 24px;
      font-weight: bold;
      pointer-events: auto;
      background-color: rgba(0, 0, 0, 0.6);
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: #fff;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.8);
        border-color: rgba(255, 255, 255, 0.6);
        transform: scale(1.1);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      &.nav-prev {
        left: 20px;
      }

      &.nav-next {
        right: 20px;
      }
    }
  }

  .shortcuts-hint {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    color: #ccc;
    z-index: 10;
    opacity: 0.8;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }

    p {
      margin: 0;
    }
  }
}

// 全屏模式样式
:global(.video-viewer:fullscreen) {
  .shortcuts-hint {
    bottom: 40px;
  }
}
</style>
