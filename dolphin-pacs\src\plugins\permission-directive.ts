import type { App, Directive } from "vue"
import { isArray } from "@@/utils/validate"
import { useUserStore } from "@/pinia/stores/user"

/**
 * @name 权限指令
 * @description 和权限判断函数 checkPermission 功能类似
 */
const permission: Directive = {
  mounted(el, binding) {
    const { value: permissionRoles } = binding
    const { roles } = useUserStore()
    if (isArray(permissionRoles) && permissionRoles.length > 0) {
      const hasPermission = roles.some(role => permissionRoles.includes(role))
      if (!hasPermission) {
        // 安全地移除元素，避免parentNode为null的错误
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        } else {
          // 如果没有父节点，直接隐藏元素
          el.style.display = 'none'
        }
      }
    } else {
      throw new Error(`参数必须是一个数组且长度大于 0，参考：v-permission="['admin', 'editor']"`)
    }
  },
  updated(el, binding) {
    // 在组件更新时重新检查权限
    const { value: permissionRoles } = binding
    const { roles } = useUserStore()
    if (isArray(permissionRoles) && permissionRoles.length > 0) {
      const hasPermission = roles.some(role => permissionRoles.includes(role))
      if (!hasPermission) {
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        } else {
          el.style.display = 'none'
        }
      } else {
        // 如果有权限，确保元素可见
        if (el.style.display === 'none') {
          el.style.display = ''
        }
      }
    }
  }
}

export function installPermissionDirective(app: App) {
  app.directive("permission", permission)
}
